using Zin.Domain.Enums;

namespace Zin.Application.DTOs.Pagamentos.Lotes
{
    public class LotePagamentoDetalheDTO
    {
        public int Id { get; set; }
        public DateTime DataProcessamento { get; set; }
        public string Usuario { get; set; } = string.Empty;
        public string NomeArquivo { get; set; } = string.Empty;
        public StatusLotePagamento Status { get; set; }
        public string StatusDescricao { get; set; } = string.Empty;
        public string? MensagemErro { get; set; }
        public DateTime DataCriacao { get; set; }
        public DateTime? DataAtualizacao { get; set; }
        
        // Métricas detalhadas
        public int TotalLidos { get; set; }
        public int TotalProgramados { get; set; }
        public int TotalLiquidados { get; set; }
        public int TotalCriados { get; set; }
        public int TotalIgnorados { get; set; }
        public int TotalDuplicados { get; set; }
        public int TotalErros { get; set; }
        
        // Propriedades calculadas
        public int TotalSucesso => TotalProgramados + TotalLiquidados;
        public decimal PercentualSucesso => TotalLidos > 0 ? (decimal)TotalSucesso / TotalLidos * 100 : 0;
        public bool TemErros => TotalErros > 0;
        public bool TemArquivo => !string.IsNullOrEmpty(NomeArquivo);
    }
}
