using AutoMapper;
using Moq;
using System.Linq.Expressions;
using Zin.Application.DTOs.Ressarcimentos;
using Zin.Application.Services.ZinPag;
using Zin.Domain.Entidades.ZinPag.Pagamentos;
using Zin.Domain.Entidades.ZinPag.Ressarcimentos;
using Zin.Domain.Enums;
using Zin.Domain.Repositorios.ZinPag;
using System.Linq;

namespace Zin.Application.Tests.Services.ZinPag
{
    public class RessarcimentoServiceTests
    {
        private readonly Mock<IRessarcimentoRepository> _ressarcimentoRepositoryMock;
        private readonly Mock<IPagamentoRepository> _pagamentoRepositoryMock;
        private readonly Mock<IMapper> _mapperMock;
        private readonly RessarcimentoService _service;

        public RessarcimentoServiceTests()
        {
            _ressarcimentoRepositoryMock = new Mock<IRessarcimentoRepository>();
            _pagamentoRepositoryMock = new Mock<IPagamentoRepository>();
            _mapperMock = new Mock<IMapper>();
            _service = new RessarcimentoService(
                _ressarcimentoRepositoryMock.Object,
                _mapperMock.Object,
                _pagamentoRepositoryMock.Object
            );
        }

        [Fact]
        public async Task CriarRessarcimentoAsync_SeExistirPagamentoEmAberto_ThrowsInvalidOperationException()
        {
            // Arrange
            var dto = new CriaRessarcimentoDto
            {
                IdAgregador = 5,
                IdPessoa = 1,
                Valor = 100,
                ItensVersoesIds = new List<int>()
                {
                    1, 2, 3
                }
            };

            var pagamentosEmAberto = new List<Pagamento>
            {
                new Pagamento
                {
                    Id = 7,
                    IdAgregador = 5,
                    StatusPagamento = StatusPagamento.Pendente,
                    PagamentoItemVersoes = new List<PagamentoItemVersao>
                    {
                        new PagamentoItemVersao { Id = 1 },
                        new PagamentoItemVersao { Id = 2 },
                        new PagamentoItemVersao { Id = 3 }
                    }
                }
            };

            _pagamentoRepositoryMock
                .Setup(r => r.BuscarAsync(It.IsAny<Expression<Func<Pagamento, bool>>>()))
                .ReturnsAsync(pagamentosEmAberto);

            // Act & Assert
            await Assert.ThrowsAsync<InvalidOperationException>(() =>
                _service.CriarRessarcimentoAsync(dto)
            );
        }

        [Fact]
        public async Task CriarRessarcimentoAsync_SeNaoExistirPagamentoEmAberto_RetornaIdRessarcimento()
        {
            // Arrange
            var dto = new CriaRessarcimentoDto
            {
                IdAgregador = 1,
                IdPessoa = 2,
                Valor = 100,
                ItensVersoesIds = new List<int>()
            };

            _pagamentoRepositoryMock
                .Setup(r => r.BuscarAsync(It.IsAny<Expression<Func<Pagamento, bool>>>()))
                .ReturnsAsync(new List<Pagamento>());

            _ressarcimentoRepositoryMock
                .Setup(r => r.InserirAsync(It.IsAny<Ressarcimento>()))
                .ReturnsAsync(123);

            // Act
            var result = await _service.CriarRessarcimentoAsync(dto);

            // Assert
            Assert.Equal(123, result);
        }
    }
}