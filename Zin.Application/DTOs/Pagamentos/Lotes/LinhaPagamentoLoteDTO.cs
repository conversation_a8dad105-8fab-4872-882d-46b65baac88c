using Zin.Domain.Enums;

namespace Zin.Application.DTOs.Pagamentos.Lotes
{
    public class LinhaPagamentoLoteDTO
    {
        public int Id { get; set; }
        public int NumeroLinhaExcel { get; set; }
        
        // Chaves de busca
        public string? CnpjPagador { get; set; }
        public string? CnpjCpfFavorecido { get; set; }
        public string? NumeroNF { get; set; }
        
        // Datas
        public DateTime? DataProgramacao { get; set; }
        public DateTime? DataLiquidacao { get; set; }
        public decimal? ValorPago { get; set; }
        
        // Resultado do processamento
        public OperacaoLinhaPagamento Operacao { get; set; }
        public string OperacaoDescricao { get; set; } = string.Empty;
        public StatusLinhaPagamento Status { get; set; }
        public string StatusDescricao { get; set; } = string.Empty;
        public string? Mensagem { get; set; }
        
        // Informações do pagamento
        public bool PagamentoEncontrado { get; set; }
        public bool PagamentoCriado { get; set; }
        public int? IdPagamento { get; set; }
        
        // Auditoria
        public DateTime DataCriacao { get; set; }
        public DateTime? DataAtualizacao { get; set; }
        
        // Propriedades calculadas
        public bool PodeReprocessar => Status == StatusLinhaPagamento.Erro || Operacao == OperacaoLinhaPagamento.Ignorado;
        public string ResultadoBusca => PagamentoCriado ? "Criado" : (PagamentoEncontrado ? "Encontrado" : "Não encontrado");
    }
}
