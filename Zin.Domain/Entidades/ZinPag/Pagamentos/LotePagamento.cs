using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Zin.Domain.Enums;

namespace Zin.Domain.Entidades.ZinPag.Pagamentos
{
    [Table("lotes_pagamentos", Schema = "zinpag")]
    public class LotePagamento
    {
        [Key]
        [Column("id_lote_pagamento")]
        public int Id { get; set; }

        [Column("data_processamento")]
        public DateTime DataProcessamento { get; set; }

        [Column("usuario")]
        [MaxLength(255)]
        public string Usuario { get; set; } = string.Empty;

        [Column("nome_arquivo")]
        [MaxLength(500)]
        public string NomeArquivo { get; set; } = string.Empty;

        [Column("arquivo_original")]
        public byte[]? ArquivoOriginal { get; set; }

        [Column("content_type")]
        [MaxLength(100)]
        public string? ContentType { get; set; }

        [Column("status")]
        public StatusLotePagamento Status { get; set; }

        [Column("cnpj_cliente")]
        [MaxLength(14)]
        public string CnpjCliente { get; set; } = string.Empty;

        // Métricas do lote
        [Column("total_lidos")]
        public int TotalLidos { get; set; }

        [Column("total_programados")]
        public int TotalProgramados { get; set; }

        [Column("total_liquidados")]
        public int TotalLiquidados { get; set; }

        [Column("total_criados")]
        public int TotalCriados { get; set; }

        [Column("total_ignorados")]
        public int TotalIgnorados { get; set; }

        [Column("total_duplicados")]
        public int TotalDuplicados { get; set; }

        [Column("total_erros")]
        public int TotalErros { get; set; }

        [Column("mensagem_erro")]
        [MaxLength(1000)]
        public string? MensagemErro { get; set; }

        [Column("data_criacao")]
        public DateTime DataCriacao { get; set; } = DateTime.UtcNow;

        [Column("data_atualizacao")]
        public DateTime? DataAtualizacao { get; set; }

        // Relacionamentos
        public ICollection<LinhaPagamentoLote> Linhas { get; set; } = [];

        // Métodos de conveniência
        public void AtualizarMetricas()
        {
            TotalLidos = Linhas.Count;
            TotalProgramados = Linhas.Count(l => l.Operacao == OperacaoLinhaPagamento.Programar && l.Status == StatusLinhaPagamento.Ok);
            TotalLiquidados = Linhas.Count(l => l.Operacao == OperacaoLinhaPagamento.Liquidar && l.Status == StatusLinhaPagamento.Ok);
            TotalCriados = Linhas.Count(l => l.PagamentoCriado);
            TotalIgnorados = Linhas.Count(l => l.Operacao == OperacaoLinhaPagamento.Ignorado);
            TotalDuplicados = Linhas.Count(l => l.Operacao == OperacaoLinhaPagamento.Duplicado);
            TotalErros = Linhas.Count(l => l.Status == StatusLinhaPagamento.Erro);

            // Determinar status do lote
            if (TotalErros == TotalLidos)
            {
                Status = StatusLotePagamento.Falha;
            }
            else if (TotalErros > 0)
            {
                Status = StatusLotePagamento.ConcluidoComErros;
            }
            else
            {
                Status = StatusLotePagamento.Concluido;
            }

            DataAtualizacao = DateTime.UtcNow;
        }

        public static LotePagamento Criar(string usuario, string nomeArquivo, string cnpjCliente, byte[]? arquivoOriginal = null, string? contentType = null)
        {
            return new LotePagamento
            {
                Usuario = usuario,
                NomeArquivo = nomeArquivo,
                CnpjCliente = cnpjCliente,
                ArquivoOriginal = arquivoOriginal,
                ContentType = contentType,
                Status = StatusLotePagamento.Processando,
                DataProcessamento = DateTime.UtcNow,
                DataCriacao = DateTime.UtcNow
            };
        }
    }
}
