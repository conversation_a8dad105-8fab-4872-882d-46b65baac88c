namespace Zin.Application.DTOs.Pagamentos.Lotes
{
    public class ResultadoPaginadoDTO<T>
    {
        public IEnumerable<T> Itens { get; set; } = [];
        public int TotalItens { get; set; }
        public int Pagina { get; set; }
        public int TamanhoPagina { get; set; }
        public int TotalPaginas => (int)Math.Ceiling((double)TotalItens / TamanhoPagina);
        public bool TemProximaPagina => Pagina < TotalPaginas;
        public bool TemPaginaAnterior => Pagina > 1;
    }
}
