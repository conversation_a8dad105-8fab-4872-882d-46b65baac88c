using System.Linq.Expressions;
using Zin.Application.DTOs.Pagamentos.Lotes;
using Zin.Domain.Entidades.ZinPag.Pagamentos;

namespace Zin.Domain.Repositorios.ZinPag
{
    public interface ILotePagamentoRepository : IRepositoryBase<LotePagamento, int>
    {
        Task<(IEnumerable<LotePagamento> lotes, int total)> BuscarPaginadoAsync(
            Expression<Func<LotePagamento, bool>>? filtro = null,
            int pagina = 1,
            int tamanhoPagina = 20,
            Expression<Func<LotePagamento, object>>? ordenacao = null,
            bool ordenacaoDecrescente = true);

        Task<LotePagamento?> BuscarComLinhasAsync(int id);
        
        Task<bool> ExisteHashLinhaAsync(string hash);
        
        Task<IEnumerable<LotePagamento>> BuscarPorPeriodoAsync(DateTime dataInicio, DateTime dataFim);
        
        Task<IEnumerable<LotePagamento>> BuscarPorUsuarioAsync(string usuario);
    }
}
