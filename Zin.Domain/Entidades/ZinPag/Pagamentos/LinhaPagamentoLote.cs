using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using Zin.Domain.Enums;

namespace Zin.Domain.Entidades.ZinPag.Pagamentos
{
    [Table("linhas_pagamento_lote", Schema = "zinpag")]
    public class LinhaPagamentoLote
    {
        [Key]
        [Column("id_linha_pagamento_lote")]
        public int Id { get; set; }

        [Column("id_lote_pagamento")]
        public int IdLotePagamento { get; set; }

        [Column("numero_linha_excel")]
        public int NumeroLinhaExcel { get; set; }

        // Chaves de busca
        [Column("cnpj_pagador")]
        [MaxLength(14)]
        public string? CnpjPagador { get; set; }

        [Column("cnpj_cpf_favorecido")]
        [MaxLength(14)]
        public string? CnpjCpfFavorecido { get; set; }

        [Column("numero_nf")]
        [MaxLength(100)]
        public string? NumeroNF { get; set; }

        // Datas
        [Column("data_programacao")]
        public DateTime? DataProgramacao { get; set; }

        [Column("data_liquidacao")]
        public DateTime? DataLiquidacao { get; set; }

        [Column("valor_pago")]
        [Precision(18, 2)]
        public decimal? ValorPago { get; set; }

        // Resultado do processamento
        [Column("operacao")]
        public OperacaoLinhaPagamento Operacao { get; set; }

        [Column("status")]
        public StatusLinhaPagamento Status { get; set; }

        [Column("mensagem")]
        [MaxLength(1000)]
        public string? Mensagem { get; set; }

        [Column("pagamento_encontrado")]
        public bool PagamentoEncontrado { get; set; }

        [Column("pagamento_criado")]
        public bool PagamentoCriado { get; set; }

        [Column("id_pagamento")]
        public int? IdPagamento { get; set; }

        // Dados originais do Excel (para auditoria)
        [Column("dados_originais_json")]
        public string? DadosOriginaisJson { get; set; }

        // Hash para controle de duplicatas
        [Column("hash_linha")]
        [MaxLength(64)]
        public string? HashLinha { get; set; }

        [Column("data_criacao")]
        public DateTime DataCriacao { get; set; } = DateTime.UtcNow;

        [Column("data_atualizacao")]
        public DateTime? DataAtualizacao { get; set; }

        // Relacionamentos
        [ForeignKey(nameof(IdLotePagamento))]
        public LotePagamento? LotePagamento { get; set; }

        [ForeignKey(nameof(IdPagamento))]
        public Pagamento? Pagamento { get; set; }

        // Métodos de conveniência
        public void MarcarComoErro(string mensagem)
        {
            Status = StatusLinhaPagamento.Erro;
            Operacao = OperacaoLinhaPagamento.Erro;
            Mensagem = mensagem;
            DataAtualizacao = DateTime.UtcNow;
        }

        public void MarcarComoSucesso(OperacaoLinhaPagamento operacao, string mensagem, int? pagamentoId = null, bool pagamentoCriado = false, bool pagamentoEncontrado = false)
        {
            Status = StatusLinhaPagamento.Ok;
            Operacao = operacao;
            Mensagem = mensagem;
            IdPagamento = pagamentoId;
            PagamentoCriado = pagamentoCriado;
            PagamentoEncontrado = pagamentoEncontrado;
            DataAtualizacao = DateTime.UtcNow;
        }

        public void MarcarComoIgnorado(string motivo)
        {
            Status = StatusLinhaPagamento.Ok;
            Operacao = OperacaoLinhaPagamento.Ignorado;
            Mensagem = motivo;
            DataAtualizacao = DateTime.UtcNow;
        }

        public void MarcarComoDuplicado(string mensagem)
        {
            Status = StatusLinhaPagamento.Ok;
            Operacao = OperacaoLinhaPagamento.Duplicado;
            Mensagem = mensagem;
            DataAtualizacao = DateTime.UtcNow;
        }

        public static LinhaPagamentoLote Criar(int idLotePagamento, int numeroLinhaExcel, string? cnpjPagador, string? cnpjCpfFavorecido, string? numeroNF, DateTime? dataProgramacao, DateTime? dataLiquidacao, decimal? valorPago, string? dadosOriginaisJson, string? hashLinha)
        {
            return new LinhaPagamentoLote
            {
                IdLotePagamento = idLotePagamento,
                NumeroLinhaExcel = numeroLinhaExcel,
                CnpjPagador = cnpjPagador,
                CnpjCpfFavorecido = cnpjCpfFavorecido,
                NumeroNF = numeroNF,
                DataProgramacao = dataProgramacao,
                DataLiquidacao = dataLiquidacao,
                ValorPago = valorPago,
                DadosOriginaisJson = dadosOriginaisJson,
                HashLinha = hashLinha,
                Status = StatusLinhaPagamento.Desconhecido,
                Operacao = OperacaoLinhaPagamento.Desconhecido,
                DataCriacao = DateTime.UtcNow
            };
        }
    }
}
